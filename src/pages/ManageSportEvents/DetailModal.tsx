import React, { useState, useEffect } from 'react';
import axios from 'axios';
import dayjs from 'dayjs';
import { Modal, Form, Input, Row, Col, Divider, DatePicker, Button, message, Radio } from 'antd';
import MarkdownEditor from '@/components/MarkdownEditor';
import ImageUpload from '@/components/ImageUpload';
import {
  getLocalStorage,
  clearFormData,
  decodeFormData,
  formatRangePickerDates,
  parseRulesContent,
  encodeBase64,
  calculateImageUrl,
} from '@/utils';

interface DetailModalProps {
  visible: boolean;
  record: any;
  onCancel: () => void;
  onEdit: (eventParams: any) => Promise<void>;
}

const currentYear = dayjs().year();
const minDate = dayjs(`${currentYear}-01-01`);
const maxDate = dayjs(`${currentYear + 3}-12-31`);

const DetailModal: React.FC<DetailModalProps> = ({ visible, record, onCancel, onEdit }) => {
  const [form] = Form.useForm();
  const [slugLength, setSlugLength] = useState(0);
  const [loading, setLoading] = useState<boolean>(false);

  useEffect(() => {
    if (record) {
      const languages = record.title.map((item: { language: string }) => item.language);
      const slug = record.slug.find(
        (item: { language: string }) => item.language === 'en'
      )?.content;
      const slugWithoutTimestamp = slug ? slug.replace(/_\d+$/, '') : '';
      setSlugLength(slugWithoutTimestamp.length);

      const fieldsValue = {
        ...record,
        date_range: [dayjs.utc(record.start_date), dayjs.utc(record.end_date)],
        rules: parseRulesContent(record.rules),
        slug: slugWithoutTimestamp,
        image: record.image ? [{ url: record.image }] : [],
        icon: record.icon ? [{ url: record.icon }] : [],
      };

      languages.forEach((language: string) => {
        fieldsValue[`slug_${language}`] =
          record.slug.find(
            (item: { language: string; content: string }) => item.language === language
          )?.content || '';
        fieldsValue[`title_${language}`] =
          record.title.find(
            (item: { language: string; content: string }) => item.language === language
          )?.content || '';
        fieldsValue[`description_${language}`] =
          record.description.find(
            (item: { language: string; content: string }) => item.language === language
          )?.content || '';
        fieldsValue[`multimedia_url_${language}`] =
          record.multimedia_url.find(
            (item: { language: string; content: string }) => item.language === language
          )?.content || '';
      });
      form.setFieldsValue(fieldsValue);
    }
  }, [record]);

  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      const { start_date, end_date } = formatRangePickerDates(values.date_range);
      const { slug, title, description, multimedia_url } = decodeFormData(values);
      const updated_by = getLocalStorage('userWallet');
      const clearValues = clearFormData(values);

      const formattedValues = {
        ...clearValues,
        updated_by,
        start_date,
        end_date,
        slug,
        title,
        description,
        rules: encodeBase64(values.rules),
        icon: clearValues.icon ? calculateImageUrl(clearValues, 'icon') : '',
        image: clearValues.image ? calculateImageUrl(clearValues, 'image') : '',
        multimedia_url: multimedia_url,
      };

      await onEdit(formattedValues);
      setLoading(false);
      onCancel();
    } catch (error) {
      setLoading(false);
      handleEditError(error);
      // 不要调用 onCancel()，让模态框保持打开状态以便用户修改后重试
    }
  };

  const handleEditError = (error: any) => {
    if (axios.isAxiosError(error)) {
      console.error('Error in fetchUpdateEvent:', error);
      const errorData = error.response?.data;

      // 特殊处理 webhook 验证错误
      if (
        errorData?.error === 'received invalid response from input validation webhook' &&
        errorData?.code === 'unexpected'
      ) {
        message.error(
          'Max allowed events number reached.'
        );
        return; // 不关闭模态框，让用户可以修改后重试
      }

      // 处理权限错误
      if (errorData?.errors?.[0]?.extensions?.code === 'permission-error') {
        message.error('Permission denied: You do not have the required permissions to edit this event.');
        return;
      }

      // 处理约束违反错误
      if (errorData?.code === 'constraint-violation') {
        message.error('Data constraint violation: Please check your input data for conflicts or invalid values.');
        return;
      }

      // 处理网络错误
      if (error.code === 'NETWORK_ERROR' || !error.response) {
        message.error('Network error: Please check your connection and try again.');
        return;
      }

      // 处理其他 HTTP 错误
      const statusCode = error.response?.status;
      if (statusCode === 400) {
        message.error('Bad request: Please check your input data and try again.');
      } else if (statusCode === 401) {
        message.error('Unauthorized: Please log in again.');
      } else if (statusCode === 403) {
        message.error('Forbidden: You do not have permission to perform this action.');
      } else if (statusCode === 404) {
        message.error('Not found: The event you are trying to edit does not exist.');
      } else if (statusCode === 500) {
        message.error('Server error: Please try again later or contact support.');
      } else {
        // 通用错误处理
        const errorMessage = errorData?.error || errorData?.message || error.message;
        message.error(`Edit failed: ${errorMessage}`);
      }
    } else if (error instanceof Error) {
      // 处理 JavaScript 错误
      message.error(`Edit failed: ${error.message}`);
    } else {
      // 处理未知错误类型
      message.error('Failed to edit event: An unexpected error occurred. Please try again.');
    }
  };

  const disabledDate = (current: any) => {
    return current && (current < minDate || current > maxDate);
  };

  return (
    <Modal
      width={800}
      title={'Event Details'}
      open={visible}
      onCancel={onCancel}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          Cancel
        </Button>,
        <Button key="submit" type="primary" loading={loading} onClick={handleOk}>
          Save
        </Button>,
      ]}
    >
      {record && (
        <Form form={form} layout="inline">
          <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                label="Slug"
                name="slug"
                rules={[
                  { required: true, message: 'Slug is required' },
                  {
                    pattern: /^[a-zA-Z0-9_]+$/,
                    message: 'Only letters, numbers, and underscores are allowed',
                  },
                ]}
              >
                <Input maxLength={30} disabled suffix={`${slugLength}/30`} />
              </Form.Item>

              <Divider dashed style={{ margin: '12px 0' }} />

              {record.title.map((item: { language: string; content: string }, index: number) => (
                <div
                  key={item.language + '_' + index}
                  style={{ display: 'flex', flexDirection: 'column', gap: 4 }}
                >
                  <Form.Item label={`Title (${item.language})`} name={`title_${item.language}`}>
                    <Input />
                  </Form.Item>
                  <Form.Item
                    label={`Description (${item.language})`}
                    name={`description_${item.language}`}
                  >
                    <Input />
                  </Form.Item>
                  <Form.Item
                    name={`multimedia_url_${item.language}`}
                    label={`Media_url (${item.language})`}
                  >
                    <Input />
                  </Form.Item>
                  <Divider dashed style={{ margin: '8px 0' }} />
                </div>
              ))}
            </Col>
            <Col
              span={12}
              style={{
                borderLeft: '1px solid #f0f0f0',
                display: 'flex',
                flexDirection: 'column',
                gap: 6,
              }}
            >
              <Form.Item label="ID" name="id">
                <Input readOnly disabled />
              </Form.Item>
              <Form.Item
                label="Date:"
                name="date_range"
                layout="vertical"
                rules={[{ required: true, message: 'Please select a date range' }]}
              >
                <DatePicker.RangePicker
                  disabledDate={disabledDate}
                  showTime={{ format: 'HH:mm' }}
                  format="YYYY-MM-DD HH:mm"
                />
              </Form.Item>

              <Divider dashed style={{ margin: '12px 0' }} />

              <Row>
                <Col span={12}>
                  <Form.Item
                    name="icon"
                    label="Icon"
                    valuePropName="fileList"
                    rules={[{ required: import.meta.env.VITE_ENV !== 'dev' }]}
                    labelCol={{ span: 8 }}
                  >
                    <ImageUpload prefix="event" maxSizeKB={64} value={record.icon} />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="image"
                    label="Image"
                    valuePropName="fileList"
                    rules={[{ required: import.meta.env.VITE_ENV !== 'dev' }]}
                    labelCol={{ span: 10 }}
                  >
                    <ImageUpload prefix="event" maxSizeKB={64} value={record.image} />
                  </Form.Item>
                </Col>
              </Row>
              <Divider dashed style={{ margin: '12px 0' }} />

              <Row>
                <Col span={24}>
                  <Form.Item
                    label="Active"
                    name="active"
                    rules={[{ required: true, message: 'Please select active status' }]}
                  >
                    <Radio.Group>
                      <Radio value={true}>Yes</Radio>
                      <Radio value={false}>No</Radio>
                    </Radio.Group>
                  </Form.Item>

                  <Form.Item
                    label="Closed"
                    name="closed"
                    rules={[{ required: true, message: 'Please select closed status' }]}
                  >
                    <Radio.Group>
                      <Radio value={true}>Yes</Radio>
                      <Radio value={false}>No</Radio>
                    </Radio.Group>
                  </Form.Item>
                </Col>
              </Row>
            </Col>
          </Row>

          <Divider dashed style={{ margin: '12px 0' }} />

          <Row style={{ width: '98%' }}>
            <Col span={24}>
              <Form.Item label="Rules" name="rules">
                <MarkdownEditor value={parseRulesContent(record.rules)} />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      )}
    </Modal>
  );
};

export default DetailModal;
