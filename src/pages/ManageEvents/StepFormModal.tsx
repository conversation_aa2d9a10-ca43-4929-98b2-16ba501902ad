import React, { useState, useEffect } from 'react';
import { Modal, Steps, Button, Form, message, Progress, Tooltip } from 'antd';
import { LeftOutlined, RightOutlined, CheckOutlined } from '@ant-design/icons';
import { EventDataType } from '@/types/events';
import { getCookie } from '@/utils';
import axios from 'axios';
import { fetchUserEventLimit } from '@/api/events';
import handleReleaseEvent from '@/utils/signature/createEventSignature';
import handleReleaseQuestion from '@/utils/signature/createQuestionSignature';
import { encodeBase64 } from '@/utils';

// 导入各个步骤组件
import BasicInfoStep from './steps/BasicInfoStep';
import TagSelectionStep from './steps/TagSelectionStep';
import ContentStep from './steps/ContentStep';
import MediaStep from './steps/MediaStep';
import RulesStep from './steps/RulesStep';
import TradingStep from './steps/TradingStep';

interface StepFormModalProps {
  visible: boolean;
  setVisible: (visible: boolean) => void;
  onSubmit: (
    values: EventDataType,
    selectedTagIdList: string[],
    fullTitle?: string
  ) => Promise<void>;
  onCancel: () => void;
  fetchData: () => void;
}

const StepFormModal: React.FC<StepFormModalProps> = ({
  visible,
  setVisible,
  onCancel,
  fetchData,
}) => {
  const [form] = Form.useForm();
  const [currentStep, setCurrentStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [stepData, setStepData] = useState<any>({});
  const [selectedTagIdList, setSelectedTagIdList] = useState<string[]>([]);
  const [mdValue, setMdValue] = useState('');
  const [createdEventData, setCreatedEventData] = useState<any>(null);
  const [createdQuestionData, setCreatedQuestionData] = useState<any>(null);
  const [showPublishButton, setShowPublishButton] = useState(false);
  const [publishLoading, setPublishLoading] = useState(false);
  const [publishStep, setPublishStep] = useState<
    'idle' | 'event' | 'question' | 'completed' | 'error'
  >('idle');

  const userRole = getCookie('jwt-role');

  // 定义步骤
  const steps = [
    {
      title: 'Basic Info',
      description: 'Set language and slug',
    },
    {
      title: 'Tags',
      description: 'Select event tags',
    },
    {
      title: 'Content',
      description: 'Add title & description',
    },
    {
      title: 'Media',
      description: 'Upload images & set date',
    },
    {
      title: 'Rules',
      description: 'Configure event rules',
    },
    {
      title: 'Trading',
      description: 'Set trading parameters',
    },
  ];

  // 初始化表单默认值
  useEffect(() => {
    if (visible) {
      form.setFieldsValue({
        languages: ['en'],
        date_range: [null, null],
        icon: [],
        banner: [],
        order_min_size: 1,
        order_price_min_tick_size: 0,
      });
    }
  }, [visible, form]);

  // 监控error状态，防止意外重置
  useEffect(() => {
    if (publishStep === 'error' && !showPublishButton) {
      setShowPublishButton(true);
    }
  }, [publishStep, showPublishButton]);

  // 重置表单和状态
  const resetForm = () => {
    // 如果在error状态，不要重置，保持在发布页面
    if (publishStep === 'error') {
      return;
    }

    form.resetFields();
    setCurrentStep(0);
    setStepData({});
    setSelectedTagIdList([]);
    setMdValue('');
    setCreatedEventData(null);
    setCreatedQuestionData(null);
    setShowPublishButton(false);
    setPublishLoading(false);
    setPublishStep('idle');
  };

  // 处理取消
  const handleCancel = () => {
    // 如果在error状态，提醒用户
    if (publishStep === 'error') {
      message.warning(
        'Please note: Event was created successfully, but Question publication failed. You can manually publish the Question from the Events page.'
      );
      // 在error状态下，用户确认后才关闭
      Modal.confirm({
        title: 'Confirm Close',
        content:
          'Are you sure you want to close? The Event was created but Question publication failed.',
        onOk: () => {
          resetForm();
          onCancel();
        },
      });
      return;
    }
    resetForm();
    onCancel();
  };

  // 获取当前步骤需要验证的字段
  const getCurrentStepFields = (): string[] => {
    switch (currentStep) {
      case 0: // Basic Info
        return ['languages', 'slug'];
      case 1: // Tags
        return ['tags'];
      case 2: // Content
        const languages = stepData.languages || form.getFieldValue('languages') || ['en'];
        const contentFields: string[] = [];
        languages.forEach((lang: string, index: number) => {
          if (index === 0) {
            // 第一个语言是必填的
            contentFields.push(`title_${lang}`);
          }
        });
        return contentFields;
      case 3: // Media
        return ['icon', 'image', 'date_range'];
      case 4: // Rules
        return []; // Rules step使用自定义验证
      case 5: // Trading
        return ['order_min_size', 'order_price_min_tick_size'];
      default:
        return [];
    }
  };

  // 下一步
  const nextStep = async () => {
    try {
      // 只验证当前步骤的必填字段
      const fieldsToValidate = getCurrentStepFields();

      // 特殊处理Rules step的验证
      if (currentStep === 4) {
        // Rules step - 验证markdown值而不是表单字段
        if (!mdValue || mdValue.trim().length < 50) {
          message.error('Rules are required and must be at least 50 characters');
          return;
        }
      } else {
        // 其他步骤：验证表单字段
        if (fieldsToValidate.length > 0) {
          await form.validateFields(fieldsToValidate);
        }
      }

      // 获取所有表单值（包括未验证的字段）
      const allValues = form.getFieldsValue();
      const newStepData = { ...stepData, ...allValues };

      // 特殊处理：保存特定步骤的数据
      if (currentStep === 1) {
        // Tags step - 确保selectedTagIdList被保存
        newStepData.tags = selectedTagIdList;
      } else if (currentStep === 4) {
        // Rules step - 保存markdown值
        newStepData.mdValue = mdValue;
      } else if (currentStep === 5) {
        // Trading step - 保存交易参数
        newStepData.order_min_size = allValues.order_min_size;
        newStepData.order_price_min_tick_size = allValues.order_price_min_tick_size;
      }

      setStepData(newStepData);
      setCurrentStep(currentStep + 1);
    } catch (error) {
      console.error('Validation failed:', error);
      message.error('Please fill in all required fields correctly');
    }
  };

  // 上一步
  const prevStep = () => {
    // 获取当前表单值并保存
    const allValues = form.getFieldsValue();
    const newStepData = { ...stepData, ...allValues };

    // 特殊处理：保存特定步骤的数据
    if (currentStep === 1) {
      // Tags step - 确保selectedTagIdList被保存
      newStepData.tags = selectedTagIdList;
    } else if (currentStep === 4) {
      // Rules step - 保存markdown值
      newStepData.mdValue = mdValue;
    } else if (currentStep === 5) {
      // Trading step - 保存交易参数
      newStepData.order_min_size = allValues.order_min_size;
      newStepData.order_price_min_tick_size = allValues.order_price_min_tick_size;
    }

    setStepData(newStepData);
    setCurrentStep(currentStep - 1);
  };

  // 发布Event和Question
  const handlePublish = async () => {
    console.log('handlePublish called');
    console.log('createdEventData:', createdEventData);
    console.log('createdQuestionData:', createdQuestionData);

    if (!createdEventData || !createdQuestionData) {
      console.error(
        'Missing data - createdEventData:',
        !!createdEventData,
        'createdQuestionData:',
        !!createdQuestionData
      );
      message.error('No created data found');
      return;
    }

    setPublishLoading(true);
    setPublishStep('event');

    try {
      // 第一步：发布Event
      message.info('Step 1: Publishing Event...');
      await handleReleaseEvent(
        createdEventData,
        (loading: any) => setPublishLoading(Object.values(loading).some(Boolean)),
        message,
        () => {} // 不需要重新获取数据
      );

      setPublishStep('question');
      message.info('Step 2: Publishing Question...');

      // 第二步：发布Question
      // 处理slug字段 - 可能是编码字符串或已解码的数组
      let enQuestion = 'default-content';
      try {
        if (!createdQuestionData.slug) {
          throw new Error('slug is undefined or null');
        }

        let slugArray;
        if (typeof createdQuestionData.slug === 'string') {
          // 如果是字符串，尝试解码
          slugArray = JSON.parse(atob(createdQuestionData.slug));
        } else if (Array.isArray(createdQuestionData.slug)) {
          // 如果已经是数组，直接使用
          slugArray = createdQuestionData.slug;
        } else {
          throw new Error('slug is neither string nor array');
        }

        if (!Array.isArray(slugArray)) {
          throw new Error('slugArray is not an array');
        }

        const enSlugItem = slugArray.find((item: any) => item.language === 'en');
        enQuestion = enSlugItem?.content || 'default-content';
      } catch (error) {
        console.warn('Failed to process question slug, using default:', error);
        console.warn('Error details:', error);
      }
      const enSlug = `${enQuestion}-${createdQuestionData.id}`;
      // 创建Promise来等待Question发布完成
      await new Promise<void>((resolve, reject) => {
        let hasCompleted = false;

        // 创建自定义message对象来捕获成功和失败
        const customMessage = {
          success: () => {
            if (!hasCompleted) {
              hasCompleted = true;
              resolve();
            }
          },
          error: (msg: string) => {
            if (!hasCompleted) {
              hasCompleted = true;
              console.error('Question publication failed:', msg);
              reject(new Error(msg));
            }
          },
        };

        // 调用handleReleaseQuestion发布Question
        handleReleaseQuestion(
          createdQuestionData,
          (loading: any) => setPublishLoading(Object.values(loading).some(Boolean)),
          customMessage,
          () => {}, // 不需要重新获取数据
          {
            eventId: createdEventData.id,
            question: encodeBase64(enSlug),
          }
        );

        // 设置超时机制 - 30秒
        setTimeout(() => {
          if (!hasCompleted) {
            hasCompleted = true;
            reject(new Error('Question publication timeout'));
          }
        }, 30000);
      });

      // 只有Question也发布成功后才设置completed状态
      setPublishStep('completed');
      message.success('Event and Question published successfully!');

      // 发布完成后关闭Modal并刷新数据
      setTimeout(() => {
        setVisible(false);
        resetForm();
        fetchData();
      }, 1500);
    } catch (error) {
      console.error('Publish failed:', error);

      // 检查错误信息来判断是哪个阶段失败
      const errorMessage = error instanceof Error ? error.message : String(error);

      // 如果错误信息包含Question相关内容，或者当前在question阶段，说明是Question发布失败
      if (
        errorMessage.includes('Question') ||
        publishStep === 'question' ||
        publishStep === 'event'
      ) {
        setPublishStep('error');

        // 确保showPublishButton保持为true
        if (!showPublishButton) {
          setShowPublishButton(true);
        }

        // 保持在发布界面，不要回到成功界面
        // showPublishButton保持true，这样用户可以看到发布进度和错误信息
        // 不关闭页面，让用户看到错误信息
      } else {
        setPublishStep('idle');
        setShowPublishButton(false); // Event发布失败时回到表单
      }
    } finally {
      setPublishLoading(false);
    }
  };

  // 最终提交
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      const finalData = { ...stepData, ...values };

      setLoading(true);

      // 导入必要的工具函数
      const {
        formatRangePickerDates,
        decodeFormData,
        clearFormData,
        getLocalStorage,
        calculateImageUrl,
        encodeBase64,
        processTitles,
      } = await import('@/utils');

      // 为event_writer角色自动添加creator标签，并确保去重
      let finalTagIdList = [...new Set(selectedTagIdList)]; // 去重
      const creatorTagId = await getCreatorTagId();
      if (
        userRole === 'event_writer' &&
        creatorTagId &&
        !finalTagIdList.includes(creatorTagId.toString())
      ) {
        finalTagIdList.push(creatorTagId.toString());
      }

      // 最终去重确保没有重复的tag_id
      finalTagIdList = [...new Set(finalTagIdList)];

      // 为event_writer角色添加额外验证：除了creator标签外，还必须至少选择一个其他标签
      if (userRole === 'event_writer' && creatorTagId) {
        const nonCreatorTags = finalTagIdList.filter((tagId) => tagId !== creatorTagId.toString());
        if (nonCreatorTags.length === 0) {
          message.error(
            'You must select at least one tag in addition to the required creator tag.'
          );
          setLoading(false);
          return;
        }
      }

      const { start_date, end_date } = formatRangePickerDates(finalData.date_range);
      const fullTitle = processTitles(finalData);
      const { slug, title, description, multimedia_url } = decodeFormData(finalData);
      const updated_by = getLocalStorage('userWallet');

      const clearValues = clearFormData(finalData);

      // 从Event数据中排除Question专用字段和不需要的字段
      const { order_price_min_tick_size, mdValue: _, ...eventOnlyValues } = clearValues;

      const formattedValues = {
        ...eventOnlyValues,
        neg_risk_market_id: '',
        updated_by,
        start_date,
        end_date,
        slug,
        title,
        // description,
        active: true,
        closed: false,
        rules: encodeBase64(mdValue),
        icon: calculateImageUrl(clearValues, 'icon'),
        image: calculateImageUrl(clearValues, 'image'),
        multimedia_url: multimedia_url,
      };

      // 构造基于Event数据的Question数据结构
      const dayjs = await import('dayjs');
      const timeStamps = dayjs.default.utc().format('YYYY-MM-DDTHH:mm:ss.SSS+00:00').toString();

      // 获取Trading步骤的数据
      const tradingData = { ...stepData, ...finalData };

      const questionData = {
        slug: slug, // 使用相同的slug编码
        icon: calculateImageUrl(clearValues, 'icon'),
        image: calculateImageUrl(clearValues, 'image'),
        order_price_min_tick_size: 0, // Question使用默认值，不从Event表单获取
        start_date: start_date,
        end_date: end_date,
        question: title, // 使用Event的title作为Question的question字段
        description: description, // 使用相同的description编码
        submitted_by: updated_by,
        condition_id: `PLACEHOLDER:${timeStamps}`,
        created_at: timeStamps,
        updated_at: timeStamps,
        order_min_size: tradingData.order_min_size || 1,
        active: true,
        closed: false,
      };

      // 在控制台输出Question数据供用户查看
      console.log('Generated Question Data:', questionData);

      // 直接调用Event创建API来获取Event ID
      try {
        const { fetchAddEvent, fetchAddEventTags, fetchAddSearchEvent } = await import(
          '@/api/events'
        );

        // 创建Event
        const eventCb = await fetchAddEvent(formattedValues);

        // 添加标签
        const uniqueTagIds = [...new Set(finalTagIdList.map((id: any) => Number(id)))];
        for (const tag_id of uniqueTagIds) {
          try {
            const tagParams = { event_id: eventCb.id, tag_id: tag_id as number };
            await fetchAddEventTags(tagParams);
          } catch (error) {
            console.warn(`Tag ${tag_id} already exists for event ${eventCb.id}, skipping...`);
          }
        }

        // 添加搜索事件
        if (fullTitle) {
          try {
            await fetchAddSearchEvent(eventCb.id, fullTitle);
          } catch (error) {
            console.warn('Failed to add search event:', error);
          }
        }

        // 创建Question
        const { fetchAddQuestion, fetchLinkEventQuestion } = await import('@/api/questions');

        let questionCb: any;
        try {
          questionCb = await fetchAddQuestion(questionData);
        } catch (questionError: any) {
          console.error('Question creation failed:', questionError);
          throw new Error(`Question creation failed: ${questionError?.message || questionError}`);
        }

        if (!questionCb || !questionCb.id) {
          console.error('Question creation returned invalid response:', questionCb);
          throw new Error('Question creation did not return valid data');
        }

        // 建立question和event的关联
        await fetchLinkEventQuestion({
          event_id: eventCb.id,
          condition_id: `PLACEHOLDER:${timeStamps}`,
        });

        message.success('Event and Question created successfully and linked!');
        setCreatedEventData(eventCb);
        setCreatedQuestionData(questionCb); // 使用API返回的数据，包含ID
        setShowPublishButton(true);
      } catch (error) {
        console.error('Creation failed:', error);
        message.error('Failed to create Event and Question');
        setLoading(false);
        return;
      }

      setLoading(false);
    } catch (error) {
      console.error('Submit failed:', error);

      // 使用与原AddModal相同的错误处理逻辑
      if (error instanceof Error && error.message.includes('maximum limit of')) {
        message.error(error.message);
      } else if (axios.isAxiosError(error)) {
        const errorData = error.response?.data;

        // 处理权限错误（可能是达到创建限制）
        if (errorData?.errors?.[0]?.extensions?.code === 'permission-error') {
          try {
            const userWallet = getCookie('userWallet') || localStorage.getItem('userWallet');
            const maxEventsPerUser = userWallet
              ? await fetchUserEventLimit(userWallet)
              : parseInt(import.meta.env.VITE_MAX_EVENTS_PER_USER || '3', 10);

            message.error(
              `You have reached the maximum limit of ${maxEventsPerUser} events. Please contact an administrator if you need to create more events.`
            );
          } catch (limitError) {
            console.error('Failed to get event limit for error message:', limitError);
            message.error(
              'You have reached the maximum limit of events. Please contact an administrator if you need to create more events.'
            );
          }
        }
        // 处理唯一性约束违反错误
        else if (
          errorData?.code === 'constraint-violation' &&
          errorData?.error?.includes('event_tags_event_id_tag_id_key')
        ) {
          message.error(
            'This event-tag combination already exists. Please check if the event was already created.'
          );
        } else {
          message.error(`Add failed: ${errorData?.code || errorData?.error || 'Unknown error'}`);
        }
      } else if (error instanceof TypeError) {
        message.error(`Add failed: ${error.message}`);
      } else {
        message.error('Add failed: An unexpected error occurred');
      }

      setLoading(false);
      // 不要关闭模态框，让用户可以重试或修改
    }
  };

  // 获取creator标签ID的辅助函数
  const getCreatorTagId = async () => {
    try {
      const { fetchTagsListByRegion } = await import('@/api/tags');
      const languages = stepData.languages || form.getFieldValue('languages') || ['en'];
      const response = await fetchTagsListByRegion(languages);
      const creatorTag = response.data.tags.find((tag: any) => tag.slug === 'creator');
      return creatorTag ? creatorTag.id : null;
    } catch (error) {
      console.error('Error fetching creator tag:', error);
      return null;
    }
  };

  // 渲染当前步骤的内容
  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return <BasicInfoStep form={form} stepData={stepData} />;
      case 1:
        return (
          <TagSelectionStep
            form={form}
            stepData={stepData}
            selectedTagIdList={selectedTagIdList}
            setSelectedTagIdList={setSelectedTagIdList}
            userRole={userRole}
          />
        );
      case 2:
        return <ContentStep form={form} stepData={stepData} />;
      case 3:
        return <MediaStep form={form} stepData={stepData} />;
      case 4:
        return (
          <RulesStep form={form} stepData={stepData} mdValue={mdValue} setMdValue={setMdValue} />
        );
      case 5:
        return <TradingStep form={form} stepData={stepData} />;
      default:
        return null;
    }
  };

  // 键盘事件处理
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      if (!visible) return;

      if (event.key === 'Enter' && event.ctrlKey) {
        // Ctrl+Enter: 下一步或提交
        if (currentStep < steps.length - 1) {
          nextStep();
        } else {
          handleSubmit();
        }
      } else if (event.key === 'Escape') {
        // Escape: 取消
        handleCancel();
      }
    };

    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [visible, currentStep, steps.length]);

  // 渲染底部按钮
  const renderFooter = () => {
    return (
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          padding: '16px 24px',
          background: 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)',
          borderTop: '1px solid #e2e8f0',
        }}
      >
        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
          {currentStep > 0 && !showPublishButton && (
            <Button
              onClick={prevStep}
              icon={<LeftOutlined />}
              style={{
                borderRadius: '8px',
                fontWeight: '500',
                height: '38px',
                padding: '0 20px',
                border: '1.5px solid #e2e8f0',
                background: 'white',
                color: '#64748b',
                fontSize: '16px',
                display: 'flex',
                alignItems: 'center',
                gap: '6px',
                transition: 'all 0.2s ease',
              }}
            >
              Previous
            </Button>
          )}
        </div>

        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
          <Button
            onClick={handleCancel}
            style={{
              borderRadius: '8px',
              fontWeight: '500',
              height: '38px',
              padding: '0 20px',
              border: '1.5px solid #e2e8f0',
              background: 'white',
              color: '#64748b',
              fontSize: '16px',
              transition: 'all 0.2s ease',
            }}
          >
            Cancel
          </Button>

          {showPublishButton ? (
            // 显示发布按钮
            <div style={{ display: 'flex', gap: '12px', alignItems: 'center' }}>
              <Button
                onClick={() => {
                  setVisible(false);
                  resetForm();
                  fetchData();
                }}
                style={{
                  borderRadius: '8px',
                  fontWeight: '500',
                  height: '38px',
                  padding: '0 20px',
                  border: '1.5px solid #e2e8f0',
                  background: 'white',
                  color: '#64748b',
                  fontSize: '16px',
                  transition: 'all 0.2s ease',
                }}
              >
                Skip Publish
              </Button>
              <Tooltip title="Publish Event and Question to blockchain" placement="top">
                <Button
                  type="primary"
                  onClick={handlePublish}
                  loading={publishLoading}
                  icon={
                    publishStep === 'completed' ? (
                      <CheckOutlined />
                    ) : publishStep === 'error' ? (
                      <CheckOutlined />
                    ) : (
                      <RightOutlined />
                    )
                  }
                  iconPosition="end"
                  style={{
                    borderRadius: '8px',
                    fontWeight: '600',
                    height: '38px',
                    padding: '0 24px',
                    background:
                      publishStep === 'completed'
                        ? 'linear-gradient(135deg, #059669 0%, #10b981 100%)'
                        : publishStep === 'error'
                          ? 'linear-gradient(135deg, #f59e0b 0%, #f97316 100%)'
                          : 'linear-gradient(135deg, #7c3aed 0%, #8b5cf6 100%)',
                    border: 'none',
                    boxShadow: '0 4px 12px rgba(124, 58, 237, 0.3)',
                    fontSize: '16px',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '6px',
                    transition: 'all 0.2s ease',
                  }}
                >
                  {publishLoading
                    ? `Publishing ${publishStep === 'event' ? 'Event' : 'Question'}...`
                    : publishStep === 'completed'
                      ? '✅ Published'
                      : publishStep === 'error'
                        ? '⚠️ Partial Success'
                        : '🚀 Publish'}
                </Button>
              </Tooltip>
            </div>
          ) : currentStep < steps.length - 1 ? (
            <Tooltip title="Press Ctrl+Enter to continue" placement="top">
              <Button
                type="primary"
                onClick={nextStep}
                icon={<RightOutlined />}
                iconPosition="end"
                style={{
                  borderRadius: '8px',
                  fontWeight: '600',
                  height: '38px',
                  padding: '0 20px',
                  background: 'linear-gradient(135deg, #0ea5e9 0%, #3b82f6 100%)',
                  border: 'none',
                  fontSize: '16px',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '6px',
                  transition: 'all 0.2s ease',
                }}
              >
                Next Step
              </Button>
            </Tooltip>
          ) : (
            <Tooltip title="Press Ctrl+Enter to create event" placement="top">
              <Button
                type="primary"
                onClick={handleSubmit}
                loading={loading}
                icon={<CheckOutlined />}
                iconPosition="end"
                style={{
                  borderRadius: '8px',
                  fontWeight: '600',
                  height: '38px',
                  padding: '0 24px',
                  background: 'linear-gradient(135deg, #059669 0%, #10b981 100%)',
                  border: 'none',
                  boxShadow: '0 4px 12px rgba(5, 150, 105, 0.3)',
                  fontSize: '16px',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '6px',
                  transition: 'all 0.2s ease',
                }}
              >
                {loading ? 'Creating...' : '🚀 Create Event'}
              </Button>
            </Tooltip>
          )}
        </div>
      </div>
    );
  };

  return (
    <>
      {/* 添加美化样式和动画 */}
      <style>
        {`
          @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
          }

          @keyframes slideInUp {
            from {
              opacity: 0;
              transform: translateY(20px);
            }
            to {
              opacity: 1;
              transform: translateY(0);
            }
          }

          @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
          }

          .modal-title-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
          }



          .step-modal-content {
            animation: slideInUp 1s ease-out;
          }

          .step-progress-bar {
            width: 100% !important;
          }

          .step-progress-bar .ant-progress-outer {
            width: 100% !important;
          }

          .step-progress-bar .ant-progress-inner {
            width: 100% !important;
          }

          .step-progress-bar .ant-progress-bg {
            background: linear-gradient(90deg, #1890ff 0%, #36cfc9 100%);
            transition: all 0.3s ease;
          }

          .step-badge {
            background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
            box-shadow: 0 2px 8px rgba(82, 196, 26, 0.3);
          }

          .step-title {
            background: linear-gradient(135deg, #1890ff 0%, #722ed1 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
          }

          /* 表单美化样式 - 只作用于StepFormModal */
          .step-form-modal .ant-form-item-label > label {
            font-size: 16px !important;
            font-weight: 600 !important;
            color: #262626 !important;
            margin-bottom: 8px !important;
          }

          /* 只对StepFormModal内部没有特定className的Input组件应用全局样式，排除custom-input */
          .step-form-modal .ant-form-item:not(.custom-input):not(.content-step-input):not(.basic-info-input):not(.trading-step-input) .ant-input,
          .step-form-modal .ant-form-item:not(.custom-input):not(.content-step-select):not(.basic-info-select) .ant-select-selector,
          .step-form-modal .ant-form-item:not(.custom-input):not(.trading-step-picker) .ant-picker,
          .step-form-modal .ant-form-item:not(.custom-input):not(.trading-step-input) .ant-input-number {
            font-size: 16px !important;
            padding: 12px 16px !important;
            border-radius: 8px !important;
            border: 1px solid #d9d9d9 !important;
            transition: all 0.3s ease !important;
            line-height: 1.5 !important;
            box-shadow: none !important;
          }

          .step-form-modal .ant-form-item:not(.custom-input):not(.content-step-input):not(.basic-info-input):not(.trading-step-input) .ant-input:hover,
          .step-form-modal .ant-form-item:not(.custom-input):not(.content-step-select):not(.basic-info-select) .ant-select-selector:hover,
          .step-form-modal .ant-form-item:not(.custom-input):not(.trading-step-picker) .ant-picker:hover,
          .step-form-modal .ant-form-item:not(.custom-input):not(.trading-step-input) .ant-input-number:hover {
            border-color: #40a9ff !important;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1) !important;
          }

          .step-form-modal .ant-form-item:not(.custom-input):not(.content-step-input):not(.basic-info-input):not(.trading-step-input) .ant-input:focus,
          .step-form-modal .ant-form-item:not(.custom-input):not(.content-step-select):not(.basic-info-select) .ant-select-focused .ant-select-selector,
          .step-form-modal .ant-form-item:not(.custom-input):not(.trading-step-picker) .ant-picker:focus,
          .step-form-modal .ant-form-item:not(.custom-input):not(.trading-step-input) .ant-input-number-focused {
            border-color: #1890ff !important;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
            outline: none !important;
          }

          .step-form-modal .ant-form-item-explain-error {
            font-size: 14px !important;
            margin-top: 6px !important;
          }

          .step-form-modal .ant-select-selection-item {
            font-size: 16px !important;
            line-height: 1.5 !important;
          }

          .step-form-modal .ant-picker-input > input {
            font-size: 16px !important;
          }

          .step-form-modal .ant-form-item {
            margin-bottom: 24px !important;
          }

          /* 描述文本样式 - 只作用于StepFormModal */
          .step-form-modal .ant-form-item + div {
            font-size: 14px !important;
            color: #666 !important;
            line-height: 1.4 !important;
          }

          /* 成功页面动画 */
          @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
              transform: translateY(0);
            }
            40% {
              transform: translateY(-10px);
            }
            60% {
              transform: translateY(-5px);
            }
          }

          @keyframes float {
            0%, 100% {
              transform: translateY(0px);
            }
            50% {
              transform: translateY(-10px);
            }
          }

          @keyframes pulseRing {
            0% {
              transform: translate(-50%, -50%) scale(0.8);
              opacity: 0.8;
            }
            50% {
              transform: translate(-50%, -50%) scale(1.1);
              opacity: 0.3;
            }
            100% {
              transform: translate(-50%, -50%) scale(1.3);
              opacity: 0;
            }
          }

          @keyframes shimmer {
            0% {
              background-position: -200% 0;
            }
            100% {
              background-position: 200% 0;
            }
          }
        `}
      </style>

      <Modal
        width={1000}
        title={
          <div
            style={{
              textAlign: 'center',
              position: 'relative',
            }}
          >
            <div
              className="modal-title-gradient"
              style={{
                fontSize: '28px',
                fontWeight: '700',
                letterSpacing: '0.8px',
                position: 'relative',
                display: 'inline-block',
                fontFamily: '"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif',
              }}
            >
              ✨ Create Event - Step by Step ✨
            </div>
            <div
              style={{
                position: 'absolute',
                bottom: '2px',
                left: '50%',
                transform: 'translateX(-50%)',
                width: '140px',
                height: '3px',
                background:
                  'linear-gradient(90deg, transparent 0%, #667eea 20%, #764ba2 40%, #f093fb 60%, #667eea 80%, transparent 100%)',
                borderRadius: '3px',
                opacity: 0.7,
                boxShadow: '0 1px 3px rgba(102, 126, 234, 0.3)',
              }}
            />
          </div>
        }
        open={visible}
        onCancel={handleCancel}
        footer={renderFooter()}
        maskClosable={false}
        className="step-form-modal"
        styles={{
          header: {
            padding: '16px 24px 16px 24px',
            borderBottom: '2px solid #e2e8f0',
          },
        }}
      >
        <div
          className="step-modal-content"
          style={{
            display: 'flex',
            background: 'white',
            position: 'relative',
            overflow: 'hidden',
          }}
        >
          {/* 左侧：Steps导航 */}
          <div
            style={{
              width: '300px',
              background: 'linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%)',
              padding: '28px 20px',
              borderRight: '2px solid #e2e8f0',
              display: 'flex',
              flexDirection: 'column',
            }}
          >
            {/* 进度指示 */}
            <div
              style={{
                marginBottom: '28px',
                padding: '20px',
                background: 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)',
                borderRadius: '16px',
                border: '1px solid #e2e8f0',
                boxShadow: '0 2px 8px rgba(0, 0, 0, 0.04)',
              }}
            >
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  marginBottom: '16px',
                }}
              >
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <div
                    style={{
                      width: '8px',
                      height: '8px',
                      borderRadius: '50%',
                      background: 'linear-gradient(135deg, #0ea5e9 0%, #3b82f6 100%)',
                      boxShadow: '0 0 8px rgba(14, 165, 233, 0.4)',
                    }}
                  ></div>
                  <span
                    style={{
                      fontSize: '16px',
                      color: '#374151',
                      fontWeight: '600',
                      letterSpacing: '0.3px',
                    }}
                  >
                    Step {currentStep + 1} of {steps.length}
                  </span>
                </div>
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px',
                  }}
                >
                  <span
                    style={{
                      fontSize: '15px',
                      color: '#0ea5e9',
                      fontWeight: '700',
                      background: 'linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%)',
                      padding: '4px 10px',
                      borderRadius: '12px',
                      border: '1px solid #bae6fd',
                      boxShadow: '0 2px 4px rgba(14, 165, 233, 0.1)',
                    }}
                  >
                    {Math.round(((currentStep + 1) / steps.length) * 100)}%
                  </span>
                </div>
              </div>
              <Progress
                className="step-progress-bar"
                percent={((currentStep + 1) / steps.length) * 100}
                showInfo={false}
                strokeColor={{
                  '0%': '#0ea5e9',
                  '30%': '#3b82f6',
                  '70%': '#6366f1',
                  '100%': '#8b5cf6',
                }}
                trailColor="#e2e8f0"
                size={14}
                style={{
                  marginBottom: '8px',
                  width: '100%',
                }}
                strokeLinecap="round"
              />
            </div>

            {/* Steps组件 */}
            <div style={{ flex: 1 }}>
              <Steps
                current={currentStep}
                direction="vertical"
                size="small"
                items={steps.map((step, index) => ({
                  ...step,
                  title: (
                    <div
                      style={{
                        fontSize: '18px',
                        fontWeight: '600',
                        color:
                          currentStep > index
                            ? '#059669'
                            : currentStep === index
                              ? '#0ea5e9'
                              : '#94a3b8',
                        lineHeight: '1.4',
                      }}
                    >
                      {step.title}
                    </div>
                  ),
                  icon: (
                    <div
                      style={{
                        width: '32px',
                        height: '32px',
                        borderRadius: '50%',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        fontSize: '13px',
                        fontWeight: 'bold',
                        background:
                          currentStep > index
                            ? 'linear-gradient(135deg, #059669 0%, #10b981 100%)'
                            : currentStep === index
                              ? 'linear-gradient(135deg, #0ea5e9 0%, #3b82f6 100%)'
                              : '#f1f5f9',
                        color: currentStep >= index ? 'white' : '#94a3b8',
                        border:
                          currentStep === index ? '3px solid #bfdbfe' : '2px solid transparent',
                        boxShadow:
                          currentStep > index
                            ? '0 4px 12px rgba(5, 150, 105, 0.3)'
                            : currentStep === index
                              ? '0 4px 12px rgba(14, 165, 233, 0.3)'
                              : 'none',
                        transition: 'all 0.3s ease',
                      }}
                    >
                      {currentStep > index ? '✓' : index + 1}
                    </div>
                  ),
                }))}
                style={{
                  background: 'white',
                  padding: '24px 20px',
                  borderRadius: '16px',
                  boxShadow: '0 4px 16px rgba(0,0,0,0.06)',
                  border: '1px solid #e2e8f0',
                }}
              />
            </div>
          </div>

          {/* 右侧：表单内容 */}
          <div
            style={{
              flex: 1,
              position: 'relative',
              display: 'flex',
              flexDirection: 'column',
            }}
          >
            {/* 表单内容区域 - 使用flex布局合理分布 */}
            <div
              style={{
                flex: 1,
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'flex-start',
                overflow: 'auto',
              }}
            >
              <Form
                form={form}
                layout="vertical"
                preserve={false}
                validateTrigger={['onChange', 'onBlur']}
                style={{
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                }}
              >
                <div
                  style={{
                    flex: 1,
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'flex-start',
                  }}
                >
                  {showPublishButton ? (
                    // 显示创建成功页面
                    <div
                      style={{
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        justifyContent: 'center',
                        height: '100%',
                        padding: '40px',
                        textAlign: 'center',
                        background:
                          'linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 50%, #f0f9ff 100%)',
                        position: 'relative',
                        overflow: 'hidden',
                      }}
                    >
                      {/* 装饰性背景元素 */}
                      <div
                        style={{
                          position: 'absolute',
                          top: '20px',
                          right: '20px',
                          width: '100px',
                          height: '100px',
                          background: 'linear-gradient(135deg, #10b981, #059669)',
                          borderRadius: '50%',
                          opacity: '0.1',
                          animation: 'float 3s ease-in-out infinite',
                        }}
                      />
                      <div
                        style={{
                          position: 'absolute',
                          bottom: '30px',
                          left: '30px',
                          width: '80px',
                          height: '80px',
                          background: 'linear-gradient(135deg, #3b82f6, #1d4ed8)',
                          borderRadius: '50%',
                          opacity: '0.08',
                          animation: 'float 4s ease-in-out infinite reverse',
                        }}
                      />

                      {/* 成功图标 */}
                      <div
                        style={{
                          position: 'relative',
                          marginBottom: '32px',
                        }}
                      >
                        <div
                          style={{
                            fontSize: '80px',
                            animation: 'bounce 1.5s ease-in-out',
                            filter:
                              publishStep === 'error'
                                ? 'drop-shadow(0 4px 12px rgba(245, 158, 11, 0.3))'
                                : 'drop-shadow(0 4px 12px rgba(16, 185, 129, 0.3))',
                          }}
                        >
                          {publishStep === 'error' ? '⚠️' : '🎉'}
                        </div>
                        <div
                          style={{
                            position: 'absolute',
                            top: '50%',
                            left: '50%',
                            width: '120px',
                            height: '120px',
                            border:
                              publishStep === 'error' ? '3px solid #f59e0b' : '3px solid #10b981',
                            borderRadius: '50%',
                            animation: 'pulseRing 2s ease-in-out infinite',
                          }}
                        />
                      </div>

                      {/* 主标题 */}
                      <div
                        style={{
                          fontSize: '32px',
                          fontWeight: '800',
                          ...(publishStep === 'error'
                            ? {
                                // Error状态使用实色，确保文字可见
                                color: '#f59e0b',
                                textShadow: '0 2px 4px rgba(245, 158, 11, 0.3)',
                              }
                            : {
                                // 成功状态使用渐变
                                background: 'linear-gradient(135deg, #059669, #10b981)',
                                backgroundClip: 'text',
                                WebkitBackgroundClip: 'text',
                                WebkitTextFillColor: 'transparent',
                              }),
                          marginBottom: '16px',
                          letterSpacing: '-0.5px',
                        }}
                      >
                        {publishStep === 'error' ? '⚠️ Partial Success' : '🚀 Creation Successful!'}
                      </div>

                      {publishStep !== 'idle' && (
                        <div
                          style={{
                            background: 'rgba(255, 255, 255, 0.9)',
                            backdropFilter: 'blur(10px)',
                            border: '1px solid rgba(16, 185, 129, 0.2)',
                            borderRadius: '16px',
                            padding: '24px',
                            marginBottom: '24px',
                            width: '100%',
                            maxWidth: '450px',
                            boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
                            animation: 'slideInUp 0.5s ease-out',
                          }}
                        >
                          <div
                            style={{
                              fontSize: '18px',
                              fontWeight: '700',
                              marginBottom: '20px',
                              color: '#1f2937',
                              display: 'flex',
                              alignItems: 'center',
                              gap: '8px',
                            }}
                          >
                            <span style={{ fontSize: '20px' }}>🚀</span>
                            Publishing Progress
                          </div>

                          <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
                            {/* Step 1: Visibility Publication */}
                            <div
                              style={{
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                gap: '16px',
                                padding: '16px',
                                background:
                                  publishStep === 'event' ||
                                  publishStep === 'question' ||
                                  publishStep === 'completed' ||
                                  publishStep === 'error' // Error状态下Event发布是成功的
                                    ? 'rgba(16, 185, 129, 0.1)'
                                    : 'rgba(107, 114, 128, 0.1)',
                                borderRadius: '12px',
                                border: `2px solid ${
                                  publishStep === 'event' ||
                                  publishStep === 'question' ||
                                  publishStep === 'completed' ||
                                  publishStep === 'error' // Error状态下Event发布是成功的
                                    ? '#10b981'
                                    : '#e5e7eb'
                                }`,
                                transition: 'all 0.3s ease',
                              }}
                            >
                              <div
                                style={{
                                  fontSize: '24px',
                                  animation:
                                    publishStep === 'event' ||
                                    publishStep === 'question' ||
                                    publishStep === 'completed' ||
                                    publishStep === 'error' // Error状态下Event发布是成功的
                                      ? 'bounce 0.6s ease-in-out'
                                      : 'pulse 1.5s ease-in-out infinite',
                                }}
                              >
                                {publishStep === 'event' ||
                                publishStep === 'question' ||
                                publishStep === 'completed' ||
                                publishStep === 'error' // Error状态下Event发布是成功的
                                  ? '✅'
                                  : '⏳'}
                              </div>
                              <div>
                                <div
                                  style={{
                                    fontSize: '16px',
                                    fontWeight: '600',
                                    color:
                                      publishStep === 'event' ||
                                      publishStep === 'question' ||
                                      publishStep === 'completed' ||
                                      publishStep === 'error' // Error状态下Event发布是成功的
                                        ? '#059669'
                                        : '#6b7280',
                                  }}
                                >
                                  Visibility Publication
                                </div>
                                <div style={{ fontSize: '14px', color: '#6b7280' }}>
                                  {publishStep === 'event' ||
                                  publishStep === 'question' ||
                                  publishStep === 'completed' ||
                                  publishStep === 'error' // Error状态下Event发布是成功的
                                    ? 'Successfully published to blockchain, visible to users'
                                    : 'Waiting for signature confirmation...'}
                                </div>
                              </div>
                            </div>

                            {/* Step 2: Tradability Publication */}
                            <div
                              style={{
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                gap: '16px',
                                padding: '16px',
                                background:
                                  publishStep === 'question' || publishStep === 'completed'
                                    ? 'rgba(59, 130, 246, 0.1)'
                                    : publishStep === 'error'
                                      ? 'rgba(239, 68, 68, 0.1)'
                                      : 'rgba(107, 114, 128, 0.1)',
                                borderRadius: '12px',
                                border: `2px solid ${
                                  publishStep === 'question' || publishStep === 'completed'
                                    ? '#3b82f6'
                                    : publishStep === 'error'
                                      ? '#ef4444'
                                      : '#e5e7eb'
                                }`,
                                transition: 'all 0.3s ease',
                              }}
                            >
                              <div
                                style={{
                                  fontSize: '24px',
                                  animation:
                                    publishStep === 'question' || publishStep === 'completed'
                                      ? 'bounce 0.6s ease-in-out'
                                      : publishStep === 'event'
                                        ? 'pulse 1.5s ease-in-out infinite'
                                        : 'none',
                                }}
                              >
                                {publishStep === 'question' || publishStep === 'completed'
                                  ? '✅'
                                  : publishStep === 'error'
                                    ? '❌'
                                    : '⏳'}
                              </div>
                              <div>
                                <div
                                  style={{
                                    fontSize: '16px',
                                    alignItems: 'center',
                                    fontWeight: '600',
                                    color:
                                      publishStep === 'question' || publishStep === 'completed'
                                        ? '#3b82f6'
                                        : publishStep === 'error'
                                          ? '#dc2626'
                                          : '#6b7280',
                                  }}
                                >
                                  Tradability Publication
                                </div>
                                <div style={{ fontSize: '14px', color: '#6b7280' }}>
                                  {publishStep === 'question' || publishStep === 'completed'
                                    ? 'On-chain operations take longer. Please keep this interface open during confirmation.'
                                    : publishStep === 'error'
                                      ? 'Publication failed - please try manually from Events page'
                                      : publishStep === 'event'
                                        ? 'On-chain operations take longer. Please keep this interface open during confirmation.'
                                        : 'Waiting for Visibility Publication to complete'}
                                </div>
                              </div>
                            </div>
                          </div>

                          {publishStep === 'error' && (
                            <div
                              style={{
                                marginTop: '20px',
                                padding: '20px',
                                background: 'rgba(239, 68, 68, 0.1)',
                                borderRadius: '12px',
                                border: '2px solid rgba(239, 68, 68, 0.3)',
                                animation: 'slideInUp 0.5s ease-out 0.3s both',
                              }}
                            >
                              <div
                                style={{
                                  fontSize: '18px',
                                  fontWeight: '600',
                                  color: '#dc2626',
                                  marginBottom: '12px',
                                  display: 'flex',
                                  alignItems: 'center',
                                  gap: '8px',
                                }}
                              >
                                ⚠️ Question Publication Failed
                              </div>
                              <div
                                style={{ fontSize: '14px', color: '#6b7280', marginBottom: '16px' }}
                              >
                                The Event has been successfully published, but the Question
                                publication encountered an error. This may be due to network timeout
                                or CORS issues.
                              </div>
                              <div
                                style={{
                                  padding: '16px',
                                  background: 'rgba(59, 130, 246, 0.1)',
                                  borderRadius: '8px',
                                  border: '1px solid rgba(59, 130, 246, 0.2)',
                                }}
                              >
                                <div
                                  style={{
                                    fontSize: '16px',
                                    fontWeight: '600',
                                    color: '#3b82f6',
                                    marginBottom: '8px',
                                  }}
                                >
                                  📝 Next Steps:
                                </div>
                                <div
                                  style={{ fontSize: '14px', color: '#4b5563', lineHeight: '1.5' }}
                                >
                                  1. Go to the <strong>Events</strong> page
                                  <br />
                                  2. Find your newly created Event
                                  <br />
                                  3. Click <strong>"Add Question"</strong> button
                                  <br />
                                  4. Publish the Question manually from there
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  ) : (
                    renderStepContent()
                  )}
                </div>
              </Form>
            </div>
          </div>
        </div>
      </Modal>

      <style>
        {`
          .step-modal-content .ant-steps-item-title {
            font-size: 17px !important;
            font-weight: 600 !important;
            line-height: 1.4 !important;
            margin-bottom: 4px !important;
          }

          .step-modal-content .ant-steps-item-description {
            font-size: 14px !important;
            color: #64748b !important;
            margin-top: 0 !important;
            font-weight: 400 !important;
          }

          .step-modal-content .ant-steps-item-icon {
            margin-right: 16px !important;
            margin-top: 2px !important;
          }

          .step-modal-content .ant-steps-item:not(:last-child) .ant-steps-item-container .ant-steps-item-tail::after {
            background: linear-gradient(to bottom, #e2e8f0 0%, #f1f5f9 100%) !important;
            width: 2px !important;
            left: 15px !important;
          }

          .step-modal-content .ant-steps-item-finish .ant-steps-item-tail::after {
            background: linear-gradient(to bottom, #059669 0%, #10b981 100%) !important;
            width: 3px !important;
            left: 14.5px !important;
          }

          .step-modal-content .ant-steps-item-process .ant-steps-item-tail::after {
            background: linear-gradient(to bottom, #0ea5e9 0%, #3b82f6 100%) !important;
            width: 3px !important;
            left: 14.5px !important;
          }

          .step-progress-bar .ant-progress-bg {
            border-radius: 5px !important;
            background: linear-gradient(90deg, #0ea5e9 0%, #3b82f6 50%, #8b5cf6 100%) !important;
          }

          .step-progress-bar .ant-progress-inner {
            border-radius: 5px !important;
            background: #e2e8f0 !important;
            box-shadow: inset 0 1px 3px rgba(0,0,0,0.1) !important;
          }

          .step-form-modal .ant-modal-content {
            border-radius: 12px !important;
            overflow: hidden !important;
          }

          .step-form-modal .ant-modal-header {
            border-radius: 12px 12px 0 0 !important;
          }

          .step-form-modal .ant-btn-primary {
            background: linear-gradient(135deg, #0ea5e9 0%, #3b82f6 100%) !important;
            border: none !important;
            border-radius: 8px !important;
            font-weight: 600 !important;
            box-shadow: 0 4px 12px rgba(14, 165, 233, 0.3) !important;
            transition: all 0.2s ease !important;
          }

          .step-form-modal .ant-btn-primary:hover {
            background: linear-gradient(135deg, #0284c7 0%, #2563eb 100%) !important;
            transform: translateY(-1px) !important;
            box-shadow: 0 6px 16px rgba(14, 165, 233, 0.4) !important;
          }

          .step-form-modal .ant-btn-default {
            border-radius: 8px !important;
            border: 1.5px solid #e2e8f0 !important;
            font-weight: 500 !important;
            transition: all 0.2s ease !important;
          }

          .step-form-modal .ant-btn-default:hover {
            border-color: #0ea5e9 !important;
            color: #0ea5e9 !important;
            transform: translateY(-1px) !important;
          }
        `}
      </style>
    </>
  );
};

export default StepFormModal;
