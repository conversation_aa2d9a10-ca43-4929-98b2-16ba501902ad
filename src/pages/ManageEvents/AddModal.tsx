import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { Modal, Form, Input, message, DatePicker, Row, Col, Select, Divider } from 'antd';
import { EventDataType } from '@/types/events';
import { fetchTagsListByRegion } from '@/api/tags';
import { fetchUserEventLimit } from '@/api/events';
import ImageUpload from '@/components/ImageUpload';
import MarkdownEditor from '@/components/MarkdownEditor';
import StepFormModal from './StepFormModal';
import {
  LANGUAGE_OPTIONS,
  formatRangePickerDates,
  decodeFormData,
  clearFormData,
  getLocalStorage,
  calculateImageUrl,
  encodeBase64,
  processTitles,
  getCookie,
} from '@/utils';
const { RangePicker } = DatePicker;

interface EventModalProps {
  visible: boolean;
  setVisible: (visible: boolean) => void;
  onSubmit: (
    values: EventDataType,
    selectedTagIdList: string[],
    fullTitle?: string
  ) => Promise<void>;
  onCancel: () => void;
  fetchData: () => void;
}

const AddModal: React.FC<EventModalProps> = ({
  visible,
  setVisible,
  onSubmit,
  onCancel,
  fetchData,
}) => {
  const userRole = getCookie('jwt-role');

  // 如果是event_writer角色，使用步骤式表单
  if (userRole === 'event_writer') {
    return (
      <StepFormModal
        visible={visible}
        setVisible={setVisible}
        onSubmit={onSubmit}
        onCancel={onCancel}
        fetchData={fetchData}
      />
    );
  }

  // 以下是admin角色的原有表单逻辑
  const [form] = Form.useForm();
  const [options, setOptions] = useState<{ value: string; label: string; id: number }[]>([]);
  const [languages, setLanguages] = useState<string[]>(['en']);
  const [loading, setLoading] = useState<boolean>(false);
  const [selectedTagIdList, setSelectedTagIdList] = useState<string[]>([]);
  const [mdValue, setMdValue] = useState('');
  const [slugLength, setSlugLength] = useState(0);
  const [creatorTagId, setCreatorTagId] = useState<number | null>(null);

  const handleOk = async () => {
    try {
      const values = await form.validateFields();

      // 为event_writer角色自动添加creator标签，并确保去重
      let finalTagIdList = [...new Set(selectedTagIdList)]; // 去重
      if (
        userRole === 'event_writer' &&
        creatorTagId &&
        !finalTagIdList.includes(creatorTagId.toString())
      ) {
        finalTagIdList.push(creatorTagId.toString());
      }

      // 最终去重确保没有重复的tag_id
      finalTagIdList = [...new Set(finalTagIdList)];

      // 为event_writer角色添加额外验证：除了creator标签外，还必须至少选择一个其他标签
      if (userRole === 'event_writer') {
        const nonCreatorTags = finalTagIdList.filter((tagId) => tagId !== creatorTagId?.toString());
        if (nonCreatorTags.length === 0) {
          message.error(
            'You must select at least one tag in addition to the required creator tag.'
          );
          setLoading(false);
          return;
        }
      }

      setLoading(true);
      const { start_date, end_date } = formatRangePickerDates(values.date_range);
      const fullTitle = processTitles(values);
      const { slug, title, description, multimedia_url } = decodeFormData(values);
      const updated_by = getLocalStorage('userWallet');

      const clearValues = clearFormData(values);
      const formattedValues = {
        ...clearValues,
        neg_risk_market_id: '',
        updated_by,
        start_date,
        end_date,
        slug,
        title,
        description,
        active: true,
        closed: false,
        rules: encodeBase64(mdValue),
        icon: calculateImageUrl(clearValues, 'icon'),
        image: calculateImageUrl(clearValues, 'image'),
        multimedia_url: multimedia_url,
      };

      await onSubmit(formattedValues, finalTagIdList, fullTitle);
      message.success('Event added successfully');
      setLoading(false);
      setVisible(false);
      fetchData();
    } catch (error) {
      // 检查是否是事件创建限制错误
      if (error instanceof Error && error.message.includes('maximum limit of')) {
        message.error(error.message);
      } else if (axios.isAxiosError(error)) {
        const errorData = error.response?.data;

        // 处理权限错误（可能是达到创建限制）
        if (errorData?.errors?.[0]?.extensions?.code === 'permission-error') {
          try {
            const userWallet = getLocalStorage('userWallet');
            const maxEventsPerUser = userWallet
              ? await fetchUserEventLimit(userWallet)
              : parseInt(import.meta.env.VITE_MAX_EVENTS_PER_USER || '3', 10);

            message.error(
              `You have reached the maximum limit of ${maxEventsPerUser} events. Please contact an administrator if you need to create more events.`
            );
          } catch (limitError) {
            console.error('Failed to get event limit for error message:', limitError);
            message.error(
              'You have reached the maximum limit of events. Please contact an administrator if you need to create more events.'
            );
          }
        }
        // 处理唯一性约束违反错误
        else if (
          errorData?.code === 'constraint-violation' &&
          errorData?.error?.includes('event_tags_event_id_tag_id_key')
        ) {
          message.error(
            'This event-tag combination already exists. Please check if the event was already created.'
          );
        } else {
          message.error(`Add failed: ${errorData?.code || errorData?.error || 'Unknown error'}`);
        }
      } else if (error instanceof TypeError) {
        message.error(`Add failed: ${error.message}`);
      } else {
        message.error('Add failed: An unexpected error occurred');
      }

      setLoading(false);
      // 不要关闭模态框，让用户可以重试或修改
      // setVisible(false);
    }
  };

  const handleLanguageChange = (selectedLanguages: string[]) => {
    setLanguages(selectedLanguages);

    // 当语言改变时，重置Tag选择
    setSelectedTagIdList([]);
    if (visible) {
      form.setFieldsValue({ tags: [] });
    }

    // 如果没有选择语言，直接清空Tag选择
    if (selectedLanguages.length === 0) {
      return;
    }

    // 如果是event_writer角色，需要重新添加creator标签
    if (userRole === 'event_writer' && creatorTagId && visible) {
      // 延迟执行，等待新的tags加载完成
      setTimeout(() => {
        const creatorOption = options.find((opt) => opt.id === creatorTagId);
        if (creatorOption) {
          setSelectedTagIdList([creatorTagId.toString()]);
          form.setFieldsValue({ tags: [creatorOption.value] });
        }
      }, 100);
    }
  };

  const searchTags = async (region: any = languages) => {
    try {
      const response = await fetchTagsListByRegion(region);
      const seenLabels = new Set();

      const newOptions = response.data.tags
        .map((item: { id: number; label: string }) => ({
          value: item.label,
          label: item.label,
          id: item.id,
        }))
        .filter((option: any) => {
          if (seenLabels.has(option.label)) {
            return false;
          } else {
            seenLabels.add(option.label);
            return true;
          }
        });

      setOptions(newOptions);

      // 查找creator标签ID
      const creatorTag = response.data.tags.find((tag: any) => tag.slug === 'creator');
      if (creatorTag) {
        setCreatorTagId(creatorTag.id);

        // 如果是event_writer角色且当前没有选中任何标签，并且已选择语言，自动添加creator标签
        if (
          userRole === 'event_writer' &&
          selectedTagIdList.length === 0 &&
          languages.length > 0 &&
          visible
        ) {
          const creatorOption = newOptions.find((opt: any) => opt.id === creatorTag.id);
          if (creatorOption) {
            setSelectedTagIdList([creatorTag.id.toString()]);
            form.setFieldsValue({ tags: [creatorOption.value] });
          }
        }
      }
    } catch (error) {
      console.error('Error fetching tags:', error);
    }
  };

  useEffect(() => {
    if (visible) {
      searchTags(languages);
    }
  }, [languages, visible]);

  // 为event_writer角色自动设置creator标签
  useEffect(() => {
    if (userRole === 'event_writer' && creatorTagId && visible && languages.length > 0) {
      const creatorOption = options.find((opt) => opt.id === creatorTagId);
      if (creatorOption) {
        // 设置表单默认值
        const currentTags = form.getFieldValue('tags') || [];
        if (!currentTags.includes(creatorOption.value)) {
          form.setFieldsValue({
            tags: [...currentTags, creatorOption.value],
          });
        }

        // 更新selectedTagIdList
        if (!selectedTagIdList.includes(creatorTagId.toString())) {
          setSelectedTagIdList((prev) => [...prev, creatorTagId.toString()]);
        }
      }
    }
  }, [userRole, creatorTagId, visible, options, form, selectedTagIdList, languages]);

  const handleTagChange = (selectedIds: any) => {
    const matchedIds: string[] = selectedIds
      .map((selectedId: string) => {
        const option = options.find((opt: any) => opt.value === selectedId);
        return option ? option.id : null;
      })
      .filter((id: any) => id !== null)
      .map((id: number) => id.toString()); // 转换为字符串数组

    // 为event_writer角色确保creator标签始终被选中
    let finalIds: string[] = [...new Set(matchedIds)];
    if (userRole === 'event_writer' && creatorTagId) {
      const creatorIdStr = creatorTagId.toString();
      if (!finalIds.includes(creatorIdStr)) {
        finalIds.push(creatorIdStr);
      }

      // 同时更新表单值，确保creator标签在UI中也显示
      const creatorOption = options.find((opt) => opt.id === creatorTagId);
      if (creatorOption && visible) {
        const finalValues = selectedIds.includes(creatorOption.value)
          ? selectedIds
          : [...selectedIds, creatorOption.value];
        form.setFieldsValue({ tags: finalValues });
      }
    }

    // 最终确保没有重复的tag_id
    setSelectedTagIdList(Array.from(new Set(finalIds)));
  };

  return (
    <Modal
      width={800}
      title={
        <div style={{ marginBottom: '28px' }}>
          <div style={{ fontSize: '18px', fontWeight: 'bold' }}>
            Add Event
            <span
              style={{
                fontSize: '12px',
                color: '#1890ff',
                marginLeft: '8px',
                padding: '2px 8px',
                background: '#e6f7ff',
                borderRadius: '4px',
                fontWeight: 'normal',
              }}
            >
              Admin Mode
            </span>
          </div>
        </div>
      }
      open={visible}
      onOk={handleOk}
      onCancel={onCancel}
      confirmLoading={loading}
    >
      <Form form={form} layout="inline">
        <Row gutter={24}>
          <Col span={12}>
            <div
              style={{
                height: '32px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <div style={{ height: '32px' }}>Language: </div>
              <Select
                mode="multiple"
                allowClear
                style={{ width: '100%', marginBottom: 16, marginLeft: 16 }}
                placeholder="Please select Languages"
                value={languages}
                onChange={handleLanguageChange}
                options={LANGUAGE_OPTIONS}
              />
            </div>
            <Divider dashed style={{ margin: '12px 0' }} />
            <div style={{ marginBottom: '16px' }}>
              <div style={{ marginBottom: '8px' }}>
                <span style={{ color: '#ff4d4f' }}>*</span> Tags:
              </div>
              <Form.Item
                name="tags"
                rules={[
                  { required: true, message: 'Please select at least one tag' },
                  ...(userRole === 'event_writer'
                    ? [
                        {
                          validator: (_: any, value: string[]) => {
                            if (!value || value.length === 0) {
                              return Promise.reject(new Error('Please select at least one tag'));
                            }

                            // 检查除了creator标签外是否还有其他标签
                            const creatorOption = options.find((opt) => opt.id === creatorTagId);
                            const nonCreatorTags = value.filter(
                              (tag) => tag !== creatorOption?.value
                            );

                            if (nonCreatorTags.length === 0) {
                              return Promise.reject(
                                new Error(
                                  'You must select at least one tag in addition to the required creator tag'
                                )
                              );
                            }

                            return Promise.resolve();
                          },
                        },
                      ]
                    : []),
                ]}
                style={{ marginBottom: 0 }}
              >
                <Select
                  mode="multiple"
                  allowClear
                  style={{ width: '100%' }}
                  placeholder={
                    userRole === 'event_writer'
                      ? 'Please select tags (creator tag is required + at least 1 more)'
                      : 'Please select Tags'
                  }
                  value={selectedTagIdList
                    .map((id) => options.find((opt) => opt.id.toString() === id)?.value)
                    .filter(Boolean)}
                  onChange={handleTagChange}
                  onDeselect={(value) => {
                    // 阻止删除creator标签
                    if (userRole === 'event_writer' && creatorTagId) {
                      const option = options.find((opt) => opt.value === value);
                      if (option?.id === creatorTagId) {
                        // 重新添加creator标签
                        setTimeout(() => {
                          if (visible) {
                            const currentValues = form.getFieldValue('tags') || [];
                            if (!currentValues.includes(value)) {
                              form.setFieldsValue({ tags: [...currentValues, value] });
                            }
                          }
                        }, 0);
                      }
                    }
                  }}
                  options={options.map((option) => ({
                    ...option,
                    // 为event_writer角色的creator标签添加特殊标识
                    label:
                      userRole === 'event_writer' && option.id === creatorTagId
                        ? `${option.label} (Required)`
                        : option.label,
                  }))}
                  tagRender={(props) => {
                    const { label, value, closable, onClose } = props;
                    const option = options.find((opt) => opt.value === value);
                    const isCreatorTag = userRole === 'event_writer' && option?.id === creatorTagId;

                    return (
                      <span
                        style={{
                          display: 'inline-block',
                          padding: '2px 8px',
                          margin: '2px',
                          backgroundColor: isCreatorTag ? '#f5f5f5' : '#e6f7ff',
                          border: isCreatorTag ? '1px solid #d9d9d9' : '1px solid #91d5ff',
                          borderRadius: '4px',
                          fontSize: '12px',
                          color: isCreatorTag ? '#999999' : '#1890ff',
                        }}
                      >
                        {label}
                        {closable && !isCreatorTag && (
                          <span
                            style={{ marginLeft: '4px', cursor: 'pointer', color: '#1890ff' }}
                            onClick={(e) => {
                              e.stopPropagation();
                              onClose();
                            }}
                          >
                            ×
                          </span>
                        )}
                        {isCreatorTag && (
                          <span
                            style={{
                              marginLeft: '4px',
                              color: '#999999',
                              fontSize: '10px',
                              fontWeight: 'bold',
                            }}
                          >
                            REQUIRED
                          </span>
                        )}
                      </span>
                    );
                  }}
                />
              </Form.Item>
              <div style={{ marginTop: '8px' }}>
                <a
                  href="/#/tags"
                  target="_blank"
                  style={{
                    color: '#1890ff',
                    fontSize: '14px',
                    marginLeft: '8px',
                  }}
                >
                  if not found, create now
                </a>
              </div>
            </div>

            <Divider dashed style={{ margin: '12px 0' }} />

            <Form.Item
              name="slug"
              label="Slug"
              rules={[
                { required: true, message: 'Slug is required' },
                {
                  pattern: /^[a-zA-Z0-9_]+$/,
                  message: 'Only letters, numbers, and underscores are allowed',
                },
              ]}
            >
              <Input
                maxLength={30}
                onChange={(e) => setSlugLength(e.target.value.length)}
                suffix={`${slugLength}/30`}
              />
            </Form.Item>

            {languages.map((language, index) => (
              <div
                key={language + '_' + index}
                style={{ display: 'flex', flexDirection: 'column', gap: 4 }}
              >
                <Divider dashed style={{ margin: '8px 0' }} />

                <Form.Item
                  name={`title_${language}`}
                  label={`Title (${language})`}
                  rules={index === 0 ? [{ required: true, message: 'Title is required' }] : []}
                >
                  <Input />
                </Form.Item>
                <Form.Item
                  name={`description_${language}`}
                  label={`Description (${language})`}
                  rules={
                    index === 0 ? [{ required: true, message: 'Description is required' }] : []
                  }
                >
                  <Input />
                </Form.Item>
                <Form.Item name={`multimedia_url_${language}`} label={`Media_url (${language})`}>
                  <Input />
                </Form.Item>
              </div>
            ))}
          </Col>
          <Col
            span={12}
            style={{
              borderLeft: '1px solid #f0f0f0',
              display: 'flex',
              flexDirection: 'column',
              gap: 4,
            }}
          >
            <Row>
              <Col span={12}>
                <Form.Item
                  name="icon"
                  label="Icon"
                  valuePropName="fileList"
                  rules={[{ required: true }]}
                  labelCol={{ span: 8 }}
                >
                  <ImageUpload prefix="event" maxSizeKB={64} />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="image"
                  label="Image"
                  valuePropName="fileList"
                  rules={[{ required: true }]}
                  labelCol={{ span: 10 }}
                >
                  <ImageUpload prefix="event" maxSizeKB={64} />
                </Form.Item>
              </Col>
            </Row>

            <Divider dashed style={{ margin: '12px 0' }} />
            <Form.Item
              name="date_range"
              label="Date"
              rules={[{ required: true, message: 'Please select a date range' }]}
              className="mb-4"
              layout="vertical"
            >
              <RangePicker
                className="w-full p-2 border border-gray-300 rounded"
                showTime={{ format: 'HH:mm' }}
                format="YYYY-MM-DD HH:mm"
              />
            </Form.Item>
          </Col>
        </Row>
        <Divider dashed style={{ margin: '12px 0' }} />
        <Row style={{ width: '98%' }}>
          <Col span={24}>
            <Form.Item name="rules" label="Rules" rules={[{ required: true }]}>
              <MarkdownEditor value={mdValue} onChange={setMdValue} />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Modal>
  );
};

export default AddModal;
