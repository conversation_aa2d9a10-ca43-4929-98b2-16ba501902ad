import React, { useState, useEffect } from 'react';
import ReactMarkdown from 'react-markdown';
import ReactMde from 'react-mde';
import 'react-mde/lib/styles/css/react-mde-all.css';
import '@/styles/MarkdownEditor.css';

interface MarkdownEditorProps {
  value: string;
  needToolBar?: boolean;
  onChange?: (value: string) => void;
  readOnly?: boolean;
}

const MarkdownEditor: React.FC<MarkdownEditorProps> = ({
  value,
  needToolBar = true,
  onChange,
  readOnly,
}) => {
  const [selectedTab, setSelectedTab] = useState<'write' | 'preview'>('write');

  useEffect(() => {
    if (readOnly) {
      setSelectedTab('preview');
    }
  }, [readOnly]);

  return (
    <ReactMde
      value={value}
      onChange={onChange}
      selectedTab={selectedTab}
      onTabChange={setSelectedTab}
      readOnly={readOnly}
      generateMarkdownPreview={(markdown) =>
        Promise.resolve(<ReactMarkdown className="markdown-content">{markdown}</ReactMarkdown>)
      }
      childProps={{
        writeButton: {
          style: { width: '100%' },
        },
      }}
      classes={{
        toolbar: needToolBar ? '' : 'hidden',
      }}
    />
  );
};

export default MarkdownEditor;
