import React, { useState, useEffect } from 'react';
import { Upload, message } from 'antd';
import { LoadingOutlined, PlusOutlined, DeleteOutlined } from '@ant-design/icons';
import { customUpload } from '@/api/events';

interface ImageUploadProps {
  prefix: string;
  maxSizeKB: number;
  value?: any;
  onChange?: (fileList: any) => void;
  fileList?: any;
}

const getBase64 = (img: File, callback: (url: string) => void) => {
  const reader = new FileReader();
  reader.addEventListener('load', () => callback(reader.result as string));
  reader.readAsDataURL(img);
};

const ImageUpload: React.FC<ImageUploadProps> = ({
  prefix,
  maxSizeKB,
  value,
  onChange,
  fileList,
}) => {
  const [loading, setLoading] = useState(false);
  const [imageUrl, setImageUrl] = useState<string | undefined>(value);

  useEffect(() => {
    // 只有当value有值时才更新imageUrl，避免用空值覆盖已上传的图片
    if (value) {
      setImageUrl(value);
    }
  }, [value]);

  // 处理fileList prop，用于Form.Item的valuePropName="fileList"
  useEffect(() => {
    if (fileList && Array.isArray(fileList) && fileList.length > 0) {
      const file = fileList[0];
      if (file.url) {
        setImageUrl(file.url);
      }
    } else if (fileList && Array.isArray(fileList) && fileList.length === 0) {
      setImageUrl(undefined);
    }
  }, [fileList]);

  const handleCustomUpload = async (options: any) => {
    const { file, onSuccess, onError } = options;

    try {
      // 调用自定义上传函数
      customUpload({ file, onSuccess, onError, prefix });
    } catch (error) {
      onError(error);
    }
  };

  const beforeUpload = (file: File) => {
    const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
    if (!isJpgOrPng) {
      message.error('You can only upload JPG/PNG file!');
    }
    const isLtMaxSize = file.size / 1024 < maxSizeKB;
    if (!isLtMaxSize) {
      message.error(`Image must be smaller than ${maxSizeKB}KB!`);
    }
    return isJpgOrPng && isLtMaxSize;
  };

  const handleChange = (info: any) => {
    if (info.file.status === 'uploading') {
      setLoading(true);
      return;
    }
    if (info.file.status === 'done') {
      getBase64(info.file.originFileObj as File, (url) => {
        setLoading(false);
        setImageUrl(url);
        if (onChange) {
          onChange([info.file]);
        }
      });
    }
  };

  const handleRemove = () => {
    setImageUrl(undefined);
    if (onChange) {
      onChange([]);
    }
  };

  const uploadButton = (
    <button style={{ border: 0, background: 'none' }} type="button">
      {loading ? <LoadingOutlined /> : <PlusOutlined />}
      <div style={{ marginTop: 8 }}>Upload</div>
    </button>
  );

  return (
    <div style={{ position: 'relative', display: 'inline-block' }}>
      <Upload
        name="avatar"
        listType="picture-card"
        className="avatar-uploader"
        showUploadList={false}
        customRequest={handleCustomUpload}
        beforeUpload={beforeUpload}
        onChange={handleChange}
        maxCount={1} // 限制只能上传一张图片
      >
        {imageUrl ? <img src={imageUrl} alt="avatar" style={{ width: '100%' }} /> : uploadButton}
      </Upload>
      {imageUrl && (
        <button
          style={{
            position: 'absolute',
            top: 0,
            right: 0,
            background: 'rgba(255, 255, 255, 0.8)',
            border: 'none',
            cursor: 'pointer',
          }}
          onClick={handleRemove}
        >
          <DeleteOutlined />
        </button>
      )}
    </div>
  );
};

export default ImageUpload;
